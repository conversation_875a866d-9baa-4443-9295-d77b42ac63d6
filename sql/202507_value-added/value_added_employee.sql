-- 删除原表（如果存在）
DROP TABLE IF EXISTS `c_value_added_employee`;

CREATE TABLE `c_value_added_employee` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `delivery_order_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '增值交付单ID',
   `biz_type` tinyint NOT NULL COMMENT '业务类型，1-社医保，2-个税明细，3-国税账号',
   `entry_type` tinyint NOT NULL COMMENT '录入方式，1-批量新增，2-单个新增',
   `operation_type` tinyint NOT NULL COMMENT '操作方式，社医保/个税明细：1-提醒，2-更正，3-减员；国税账号：1-会计实名，2-异地实名',
   `employee_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工姓名',
   `id_number` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号',
   `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
   `gross_salary` decimal(10,2) DEFAULT NULL COMMENT '应发工资',
   `provident_fund_personal` decimal(10,2) DEFAULT NULL COMMENT '公积金（个人）',
   `tax_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '税号（国税账号业务专用，可同信用代码）',
   `query_password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '查询密码（国税账号业务专用）',
   `social_insurance` json DEFAULT NULL COMMENT '社保套餐信息，JSON格式：{"yang_lao":true,"shi_ye":true,"gong_shang":true,"yi_liao":true,"sheng_yu":true,"qi_ta":true}',
   `extend_info` json DEFAULT NULL COMMENT '扩展信息，JSON格式存储其他业务特定字段',
   `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
   `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态，1-待处理，2-已处理，3-已完成',
   `login_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录方式（个税账号业务专用）',
   `real_name_agent` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '实名经办人（个税账号业务专用）',
   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
   `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
   PRIMARY KEY (`id`),
   KEY `idx_delivery_order_biz` (`delivery_order_no`,`biz_type`),
   KEY `idx_employee_name` (`employee_name`),
   KEY `idx_biz_type_status` (`biz_type`,`status`),
   KEY `idx_tax_number` (`tax_number`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='增值员工信息表（支持社医保、个税明细、国税账号等多业务类型）';
