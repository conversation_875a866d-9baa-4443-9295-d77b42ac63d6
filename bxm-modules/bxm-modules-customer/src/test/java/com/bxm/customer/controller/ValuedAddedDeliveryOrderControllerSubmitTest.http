### submitToPendingDelivery接口HTTP测试文件
### 测试ValuedAddedDeliveryOrderController的submitToPendingDelivery方法
### 该接口先调用upsert保存数据，然后将状态转换为SUBMITTED_PENDING_DELIVERY
###
### 测试场景覆盖：
### 1. 正常提交场景 - 从草稿状态直接提交到待交付
### 2. 数据验证场景 - 测试各种必填字段验证
### 3. 状态转换场景 - 测试状态机验证逻辑
### 4. 错误处理场景 - 测试异常情况处理

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImQ2MTNlMmQ5LTAzZjUtNDI2Ny1hZjQ5LTYyYzM5NjkwMDg5MSIsInVzZXJuYW1lIjoiYWRtaW4ifQ.K9s3YbMAYoHZ2rwI8qWs0MO7qv8wGlc49OKV3FEAFYNbt5EXj5YpbjbTzlh3trI3bpzxXbp01WBu3x598dILRw

### ========================================
### 1. 大数据量提交场景
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/submit
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "*******************",
  "customerName": "大数据量测试企业集团有限责任公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 2,
  "valueAddedItem": "综合增值税务服务包含多项子服务内容",
  "valueAddedType": "综合服务",
  "itemName": "企业综合税务服务包",
  "accountPeriod": "2025-01",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "totalAmount": 999999.99,
  "contactMobile": "***********",
  "contactIdNumber": "350105199912311234",
  "customerId": 9999,
  "businessTopDeptId": 100,
  "syncHandlingFee": true,
  "syncReassignment": true,
  "syncContactPerson": true,
  "syncAccountChange": true,
  "modifyDueDate": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "VOUCHER_BASED", "MULTI_ENTITY"]
  },
  "requirements": "这是一个大数据量测试场增值税专用发票代开服务",
  "taxRequirement": "严%2",
  "ddl": "2025-12-31",
  "remark": "大数据量测试备注信息，用于验证系统对长文本字段的处理能力。"
}

### ========================================
### 2. changeStatus 状态变更测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/changeStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}
deptId: 100

{
  "targetStatus": "CONFIRMED_PENDING_DEDUCTION",
  "operTypeName":"handleExceptionDeliver",
  "remark": "测试的交付备注",
  "stock": "",
  "deliveryOrderNo": "*******************",
  "creditCode": "cs600"
}
