package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.FileStatusInfo;
import com.bxm.customer.domain.enums.ValueAddedFileType;
import com.bxm.customer.domain.enums.ValueAddedProcessStatus;
import com.bxm.customer.mapper.ValueAddedFileMapper;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.customer.service.IValueAddedStockService;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.domain.ValueAddedStock;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedStockVO;
import com.bxm.customer.utils.StockExcelParser;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.bxm.file.api.domain.ValueAddedFileDTO;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 增值交付单文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
@Service
public class ValueAddedFileServiceImpl extends ServiceImpl<ValueAddedFileMapper, ValueAddedFile>
        implements IValueAddedFileService {

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private IValueAddedStockService valueAddedStockService;

    @Autowired
    private StockExcelParser stockExcelParser;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    @Lazy
    private IValueAddedDeliveryOrderService valueAddedDeliveryOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long savePersonnelExcelFile(MultipartFile excelFile, String deliveryOrderNo) throws Exception {
        log.info("Saving personnel excel file, fileName: {}, size: {}", excelFile.getOriginalFilename(), excelFile.getSize());

        // 1. 文件大小验证
        if (excelFile.getSize() > 2 * 1024 * 1024) { // 2MB限制
            throw new IllegalArgumentException("Excel文件大小不能超过2MB");
        }

        // 2. 上传文件到文件服务
        RemoteAliFileDTO uploadResult = remoteFileService.uploadFile(excelFile).getData();
        if (uploadResult == null) {
            throw new RuntimeException("文件上传失败");
        }

        // 3. 保存文件记录到数据库
        ValueAddedFile fileRecord = new ValueAddedFile();
        fileRecord.setDeliveryOrderNo(deliveryOrderNo);
        fileRecord.setFileName(excelFile.getOriginalFilename());
        fileRecord.setFileUrl(uploadResult.getUrl());
        fileRecord.setFileSize(excelFile.getSize());
        fileRecord.setFileType(ValueAddedFileType.PERSONNEL_EXCEL.getCode()); // 人员明细excel
        fileRecord.setIsDel(false);
        fileRecord.setCreateTime(LocalDateTime.now());
        fileRecord.setUpdateTime(LocalDateTime.now());

        // 初始化处理状态 - 使用统一的工具方法构建JSON
        String initialStatus = buildInitialStatusJson();
        fileRecord.setStatus(ValueAddedProcessStatus.PROCESSING.getCode());
        fileRecord.setRemark(initialStatus);

        if (!save(fileRecord)) {
            throw new RuntimeException("保存文件记录失败");
        }

        log.info("File record saved successfully, fileId: {}, fileUrl: {}", fileRecord.getId(), uploadResult.getUrl());

        return fileRecord.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveDeliveryOrderFile(MultipartFile file, String deliveryOrderNo) throws Exception {
        log.info("Saving delivery order file, fileName: {}, size: {}, deliveryOrderNo: {}",
                file.getOriginalFilename(), file.getSize(), deliveryOrderNo);

        // 复用uploadFile方法，使用DELIVERY_MATERIAL类型
        ValueAddedFileDTO uploadResult = uploadFile(deliveryOrderNo, file, ValueAddedFileType.DELIVERY_MATERIAL);
        log.info("Delivery order file saved successfully, fileId: {}, deliveryOrderNo: {}", uploadResult.getFileId(), deliveryOrderNo);
        return uploadResult.getFileId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ValueAddedStockVO> saveStockExcelFile(MultipartFile excelFile, String deliveryOrderNo) throws Exception {
        // 上传文件到文件服务
        RemoteAliFileDTO uploadResult = uploadFileToRemote(excelFile);

        // 保存文件记录到数据库
        ValueAddedFile fileRecord = createStockFileRecord(excelFile, deliveryOrderNo, uploadResult);
        if (!save(fileRecord)) {
            throw new RuntimeException("保存文件记录失败");
        }

        // 解析Excel文件并保存库存数据
        List<ValueAddedStock> stockList = parseAndSaveStockData(excelFile, deliveryOrderNo, fileRecord);

        saveFileUploadBusinessLog(deliveryOrderNo, ValueAddedFileDTO.of(uploadResult, fileRecord.getId()));
        // 转换为VO并返回
        return ValueAddedStockVO.fromEntityList(stockList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFile(Long fileId, String deliveryOrderNo) {
        log.info("Deleting file, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);

        // 1. 参数验证
        if (fileId == null) {
            throw new IllegalArgumentException("文件ID不能为空");
        }
        if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
            throw new IllegalArgumentException("交付单编号不能为空");
        }

        // 2. 查询文件记录
        ValueAddedFile fileRecord = getById(fileId);
        if (fileRecord == null) {
            throw new IllegalArgumentException("文件不存在");
        }

        // 3. 权限验证：验证文件属于指定交付单
        if (!deliveryOrderNo.trim().equals(fileRecord.getDeliveryOrderNo())) {
            throw new IllegalArgumentException("无权限删除该文件");
        }

        // 4. 状态检查：验证文件未被删除
        if (Boolean.TRUE.equals(fileRecord.getIsDel())) {
            throw new IllegalArgumentException("文件已被删除");
        }

        // 5. 执行软删除
        fileRecord.setIsDel(true);
        fileRecord.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(fileRecord);
        if (result) {
            log.info("File deleted successfully, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);
        } else {
            log.error("Failed to delete file, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);
        }

        return result;
    }

    @Override
    public String getFileDownloadUrl(Long fileId, String deliveryOrderNo) {
        log.info("Getting file download URL, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);

        // 1. 参数验证
        if (fileId == null) {
            throw new IllegalArgumentException("文件ID不能为空");
        }
        if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
            throw new IllegalArgumentException("交付单编号不能为空");
        }

        // 2. 查询文件记录
        ValueAddedFile fileRecord = getById(fileId);
        if (fileRecord == null) {
            throw new IllegalArgumentException("文件不存在");
        }

        // 3. 权限验证：验证文件属于指定交付单
        if (!deliveryOrderNo.trim().equals(fileRecord.getDeliveryOrderNo())) {
            throw new IllegalArgumentException("无权限访问该文件");
        }

        // 4. 状态检查：验证文件未被删除
        if (Boolean.TRUE.equals(fileRecord.getIsDel())) {
            throw new IllegalArgumentException("文件已被删除");
        }

        // 5. 获取完整下载URL
        try {
            String fullFileUrl = remoteFileService.getFullFileUrl(fileRecord.getFileUrl()).getData();
            if (fullFileUrl == null || fullFileUrl.trim().isEmpty()) {
                throw new RuntimeException("获取文件下载地址失败");
            }

            log.info("File download URL retrieved successfully, fileId: {}, deliveryOrderNo: {}",
                    fileId, deliveryOrderNo);
            return fullFileUrl;
        } catch (Exception e) {
            log.error("Failed to get file download URL, fileId: {}, deliveryOrderNo: {}",
                    fileId, deliveryOrderNo, e);
            throw new RuntimeException("获取文件下载地址失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFileProcessStatus(Long fileId, Integer status, String statusInfo) {
        log.info("Updating file process status, fileId: {}, status: {}", fileId, status);

        ValueAddedFile fileRecord = getById(fileId);
        if (fileRecord == null) {
            log.warn("File record not found, fileId: {}", fileId);
            return;
        }

        // 更新状态字段和备注信息
        fileRecord.setStatus(status);
        fileRecord.setRemark(statusInfo);
        fileRecord.setUpdateTime(LocalDateTime.now());

        if (!updateById(fileRecord)) {
            log.error("Failed to update file process status, fileId: {}", fileId);
            throw new RuntimeException("更新文件处理状态失败");
        }

        log.info("File process status updated successfully, fileId: {}, status: {}", fileId, status);
    }

    @Override
    public String getFileProcessStatus(Long fileId) {
        ValueAddedFile fileRecord = getById(fileId);
        if (fileRecord == null) {
            return buildErrorStatusJson("文件记录不存在");
        }
        return fileRecord.getRemark();
    }

    @Override
    public ValueAddedFile getFileById(Long fileId) {
        return getById(fileId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueAddedFileDTO uploadFile(String deliveryOrderNo, MultipartFile file, ValueAddedFileType fileType) throws Exception {
        log.info("Uploading file, deliveryOrderNo: {}, fileName: {}, fileType: {}",
                deliveryOrderNo, file.getOriginalFilename(), fileType.getName());

        // 1. 参数验证
        Assert.hasText(deliveryOrderNo, "交付单编号不能为空");
        Assert.notNull(file, "文件不能为空");
        Assert.isTrue(!file.isEmpty(), "文件不能为空");
        Assert.notNull(fileType, "文件类型不能为空");

        // 2. 文件大小验证（根据文件类型设置不同限制）
        long maxSize = getMaxFileSizeByType(fileType);
        if (file.getSize() > maxSize) {
            throw new IllegalArgumentException(String.format("文件大小不能超过%dMB", maxSize / (1024 * 1024)));
        }

        // 3. 上传文件到文件服务
        RemoteAliFileDTO uploadResult = remoteFileService.uploadFile(file).getData();
        if (uploadResult == null) {
            throw new RuntimeException("文件上传失败");
        }

        // 4. 保存文件记录到数据库
        ValueAddedFile fileRecord = new ValueAddedFile();
        fileRecord.setDeliveryOrderNo(deliveryOrderNo.trim());
        fileRecord.setFileName(file.getOriginalFilename());
        fileRecord.setFileUrl(uploadResult.getUrl());
        fileRecord.setFileSize(file.getSize());
        fileRecord.setFileType(fileType.getCode());
        fileRecord.setIsDel(false);
        fileRecord.setCreateTime(LocalDateTime.now());
        fileRecord.setUpdateTime(LocalDateTime.now());

        // 5. 根据文件类型设置处理状态
        if (
                fileType == ValueAddedFileType.DELIVERY_ATTACHMENT ||
                        fileType == ValueAddedFileType.STANDARD_ATTACHMENT) {
            // 操作附件、交付附件、标准附件无需处理，直接设为完成
            fileRecord.setStatus(ValueAddedProcessStatus.COMPLETED.getCode());
        } else {
            // 其他类型文件可能需要处理
            fileRecord.setStatus(ValueAddedProcessStatus.PROCESSING.getCode());
            fileRecord.setRemark(buildInitialStatusJson());
        }

        if (!save(fileRecord)) {
            throw new RuntimeException("保存文件记录失败");
        }

        // 6. 创建ValueAddedFileDTO并返回
        ValueAddedFileDTO result = ValueAddedFileDTO.of(uploadResult, fileRecord.getId());

        // 7. 记录文件上传操作日志
        saveFileUploadBusinessLog(deliveryOrderNo, result);

        log.info("File uploaded successfully, fileId: {}, url: {}", fileRecord.getId(), uploadResult.getUrl());
        return result;
    }

    @Override
    public List<ValueAddedFile> getByDeliveryOrderNoAndFileTypes(String deliveryOrderNo, Integer... fileTypes) {
        if (StringUtils.isEmpty(deliveryOrderNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ValueAddedFile> wrapper = new LambdaQueryWrapper<ValueAddedFile>()
                .eq(ValueAddedFile::getDeliveryOrderNo, deliveryOrderNo.trim())
                .eq(ValueAddedFile::getIsDel, false);
        if (!ObjectUtils.isEmpty(fileTypes)) {
            wrapper.in(ValueAddedFile::getFileType, fileTypes);
        }
        return list(wrapper);
    }

    @Override
    public void deleteFileByDeliveryOrderNoAndFileTypes(String deliveryOrderNo, Integer... fileTypes) {
        if (StringUtils.isEmpty(deliveryOrderNo)) {
            return;
        }
        LambdaUpdateWrapper<ValueAddedFile> wrapper = new LambdaUpdateWrapper<ValueAddedFile>()
                .eq(ValueAddedFile::getDeliveryOrderNo, deliveryOrderNo.trim())
                .eq(ValueAddedFile::getIsDel, false);
        if (!ObjectUtils.isEmpty(fileTypes)) {
            wrapper.in(ValueAddedFile::getFileType, fileTypes);
        }
        wrapper.set(ValueAddedFile::getIsDel, true);
        update(wrapper);
    }

    /**
     * 根据文件类型获取最大文件大小限制
     *
     * @param fileType 文件类型
     * @return 最大文件大小（字节）
     */
    private long getMaxFileSizeByType(ValueAddedFileType fileType) {
        switch (fileType) {
            case PERSONNEL_EXCEL:
                return 2 * 1024 * 1024; // 2MB
            case STOCK:
                return 5 * 1024 * 1024; // 5MB
            case DELIVERY_ATTACHMENT:
            case STANDARD_ATTACHMENT:
                return 10 * 1024 * 1024; // 10MB
            default:
                return 2 * 1024 * 1024; // 默认2MB
        }
    }

    /**
     * 构建初始状态JSON
     * 使用FileStatusInfo DTO统一处理
     */
    private String buildInitialStatusJson() {
        try {
            FileStatusInfo statusInfo = FileStatusInfo.builder()
                    .status(ValueAddedProcessStatus.PROCESSING.getCode())
                    .message("文件已上传，等待处理")
                    .totalCount(0)
                    .successCount(0)
                    .failCount(0)
                    .hasErrorFile(false)
                    .updateTime(System.currentTimeMillis())
                    .build();

            // 使用FastJSON2替代ObjectMapper
            return JSON.toJSONString(statusInfo);
        } catch (Exception e) {
            log.error("Build initial status JSON failed", e);
            return "{\"status\":0,\"message\":\"文件已上传，等待处理\"}";
        }
    }

    /**
     * 构建错误状态JSON
     * 用于文件记录不存在等错误情况
     */
    private String buildErrorStatusJson(String message) {
        try {
            FileStatusInfo statusInfo = FileStatusInfo.builder()
                    .status(ValueAddedProcessStatus.FAILED.getCode())
                    .message(message)
                    .totalCount(0)
                    .successCount(0)
                    .failCount(0)
                    .hasErrorFile(false)
                    .updateTime(System.currentTimeMillis())
                    .build();

            return JSON.toJSONString(statusInfo);
        } catch (Exception e) {
            log.error("Build error status JSON failed", e);
            return "{\"status\":2,\"message\":\"" + message + "\"}";
        }
    }


    /**
     * 保存文件上传操作日志
     *
     * @param deliveryOrderNo 交付单编号
     * @param fileResult      文件上传结果
     */
    private void saveFileUploadBusinessLog(String deliveryOrderNo, ValueAddedFileDTO fileResult) {
        try {
            // 1. 通过deliveryOrderNo查询orderId
            Long orderId = getOrderIdByDeliveryOrderNo(deliveryOrderNo);
            if (orderId == null) {
                return;
            }
            // 2. 使用JSON.toJSONString处理操作内容
            String operContent = JSONObject.toJSONString(Arrays.asList(fileResult));
            // 3. 记录操作日志
            asyncLogService.saveBusinessLog(new BusinessLogDTO()
                    .setBusinessId(orderId)
                    .setBusinessType(BusinessLogBusinessType.DELIVERY_ORDER.getCode())
                    .setOperType("文件上传")
                    .setOperName(SecurityUtils.getUsername())
                    .setOperUserId(SecurityUtils.getUserId())
                    .setOperImages(operContent));
        } catch (Exception e) {
            log.error("文件上传操作日志记录失败: {}", e.getMessage(), e);
        }
    }


    /**
     * 通过交付单编号查询订单ID
     *
     * @param deliveryOrderNo 交付单编号
     * @return 订单ID，如果未找到则返回null
     */
    private Long getOrderIdByDeliveryOrderNo(String deliveryOrderNo) {
        try {
            LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNo.trim())
                    .eq(ValueAddedDeliveryOrder::getIsDel, false)
                    .select(ValueAddedDeliveryOrder::getId);
            ValueAddedDeliveryOrder order = valueAddedDeliveryOrderService.getOne(queryWrapper);
            return order != null ? order.getId() : null;
        } catch (Exception e) {
            log.error("Failed to get orderId by deliveryOrderNo: {}", deliveryOrderNo, e);
            return null;
        }
    }


    /**
     * 上传文件到远程服务
     */
    private RemoteAliFileDTO uploadFileToRemote(MultipartFile excelFile) {
        RemoteAliFileDTO uploadResult = remoteFileService.uploadFile(excelFile).getData();
        if (uploadResult == null) {
            throw new RuntimeException("文件上传失败");
        }
        return uploadResult;
    }

    /**
     * 创建标准附件文件记录
     */
    private ValueAddedFile createStockFileRecord(MultipartFile excelFile, String deliveryOrderNo, RemoteAliFileDTO uploadResult) {
        // 在创建前先删除相同deliveryOrderNo和fileType为标准附件的数据
        LambdaUpdateWrapper<ValueAddedFile> deleteWrapper = new LambdaUpdateWrapper<>();
        deleteWrapper.eq(ValueAddedFile::getDeliveryOrderNo, deliveryOrderNo.trim())
                .eq(ValueAddedFile::getFileType, ValueAddedFileType.STANDARD_ATTACHMENT.getCode())
                .eq(ValueAddedFile::getIsDel, false)
                .set(ValueAddedFile::getIsDel, true)
                .set(ValueAddedFile::getUpdateTime, LocalDateTime.now());
        update(deleteWrapper);
        ValueAddedFile fileRecord = new ValueAddedFile();
        fileRecord.setDeliveryOrderNo(deliveryOrderNo.trim());
        fileRecord.setFileName(excelFile.getOriginalFilename());
        fileRecord.setFileUrl(uploadResult.getUrl());
        fileRecord.setFileSize(excelFile.getSize());
        fileRecord.setFileType(ValueAddedFileType.STANDARD_ATTACHMENT.getCode());
        fileRecord.setIsDel(false);
        fileRecord.setStatus(ValueAddedProcessStatus.PROCESSING.getCode());
        LocalDateTime now = LocalDateTime.now();
        fileRecord.setCreateTime(now);
        fileRecord.setUpdateTime(now);

        return fileRecord;
    }

    /**
     * 解析Excel文件并保存库存数据
     */
    private List<ValueAddedStock> parseAndSaveStockData(MultipartFile excelFile, String deliveryOrderNo, ValueAddedFile fileRecord) {
        try {
            // 1. 使用stockExcelParser解析Excel文件
            List<ValueAddedStock> stockList = stockExcelParser.parseStockExcelFromMultipartFile(excelFile, deliveryOrderNo.trim());

            if (stockList == null || stockList.isEmpty()) {
                throw new RuntimeException("Excel文件中没有有效的库存数据");
            }

            // 2. 根据交付单账期范围过滤库存数据
            List<ValueAddedStock> filteredStockList = filterStockByAccountingPeriod(stockList, deliveryOrderNo.trim());

            // 3. 先删除该交付单的现有库存数据，再保存过滤后的库存数据（保障事务一致性）
            valueAddedStockService.deleteByDeliveryOrderNo(deliveryOrderNo.trim());

            boolean saveResult = valueAddedStockService.batchSaveOrUpdate(filteredStockList);
            if (!saveResult) {
                throw new RuntimeException("保存库存数据失败");
            }

            // 4. 更新文件状态为处理完成
            updateFileProcessStatus(fileRecord.getId(), ValueAddedProcessStatus.COMPLETED.getCode(),
                    String.format("解析完成，共处理%d条库存记录，过滤后保存%d条", stockList.size(), filteredStockList.size()));

            return filteredStockList;

        } catch (Exception e) {
            updateFileProcessStatus(fileRecord.getId(), ValueAddedProcessStatus.FAILED.getCode(),
                    "解析失败：" + e.getMessage());
            throw new RuntimeException("解析库存Excel文件失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据交付单账期范围过滤库存数据
     *
     * @param stockList       原始库存数据列表
     * @param deliveryOrderNo 交付单编号
     * @return 过滤后的库存数据列表
     */
    private List<ValueAddedStock> filterStockByAccountingPeriod(List<ValueAddedStock> stockList, String deliveryOrderNo) {
        try {

            // 1. 获取交付单信息
            ValueAddedDeliveryOrder deliveryOrder = valueAddedDeliveryOrderService.getByDeliveryOrderNo(deliveryOrderNo);
            if (deliveryOrder == null) {
                log.warn("Delivery order not found: {}, saving all stock data", deliveryOrderNo);
                return stockList;
            }

            Integer accountingPeriodStart = deliveryOrder.getAccountingPeriodStart();
            Integer accountingPeriodEnd = deliveryOrder.getAccountingPeriodEnd();

            // 2. 如果账期范围未设置，保存所有数据
            if (accountingPeriodStart == null || accountingPeriodEnd == null) {
                log.info("Accounting period range not set for delivery order: {}, saving all stock data", deliveryOrderNo);
                return stockList;
            }

            // 3. 过滤库存数据
            List<ValueAddedStock> filteredList = stockList.stream()
                    .filter(stock -> isStockInAccountingPeriod(stock, accountingPeriodStart, accountingPeriodEnd))
                    .collect(Collectors.toList());

            return filteredList;

        } catch (Exception e) {
            log.error("Failed to filter stock data by accounting period for delivery order: {}", deliveryOrderNo, e);
            // 发生异常时返回原始数据，避免数据丢失
            return stockList;
        }
    }

    /**
     * 判断库存数据是否在账期范围内
     *
     * @param stock                 库存数据
     * @param accountingPeriodStart 账期开始
     * @param accountingPeriodEnd   账期结束
     * @return 是否在范围内
     */
    private boolean isStockInAccountingPeriod(ValueAddedStock stock, Integer accountingPeriodStart, Integer accountingPeriodEnd) {
        try {
            String periodStr = stock.getPeriod();
            if (StringUtils.isEmpty(periodStr)) {
                log.warn("Stock period is empty, excluding from filter: {}", stock.getId());
                return false;
            }
            Integer stockPeriod = Integer.parseInt(periodStr.trim());
            // 判断是否在账期范围内
            boolean inRange = stockPeriod >= accountingPeriodStart && stockPeriod <= accountingPeriodEnd;
            if (!inRange) {
                log.debug("Stock period {} is outside accounting range [{}, {}], excluding",
                        stockPeriod, accountingPeriodStart, accountingPeriodEnd);
            }

            return inRange;

        } catch (NumberFormatException e) {
            log.warn("Failed to parse stock period as integer: {}, excluding from filter", stock.getPeriod());
            return false;
        }
    }
}
