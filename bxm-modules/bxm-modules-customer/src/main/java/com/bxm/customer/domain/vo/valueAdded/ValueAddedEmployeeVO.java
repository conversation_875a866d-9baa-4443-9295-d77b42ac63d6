package com.bxm.customer.domain.vo.valueAdded;

import com.bxm.customer.validation.IdNumber;
import com.bxm.customer.validation.SocialInsuranceBase;
import com.bxm.customer.validation.SocialInsurancePackage;
import com.bxm.customer.validation.TaxNumber;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 增值员工信息Upsert请求VO
 *
 * 用于增值员工信息的新增和更新操作，包含完整的数据验证规则。
 * 支持四种业务类型：社医保、个税明细、国税账号、个税账号。
 *
 * 注意：身份证号的校验逻辑已迁移到具体的业务策略中，
 * 个税和社医保业务会进行强制校验，其他业务类型根据需求决定。
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("增值员工信息Upsert请求")
@JsonInclude(JsonInclude.Include.NON_NULL)
@SocialInsuranceBase(message = "社保基数验证失败")
public class ValueAddedEmployeeVO {

    /** 主键ID（更新时需要） */
    @ApiModelProperty(value = "主键ID，更新时必填")
    private Long id;

    /** 增值交付单编号 */
    @NotNull(message = "增值交付单编号不能为空")
    @ApiModelProperty(value = "增值交付单编号", required = true)
    private String deliveryOrderNo;

    /** 业务类型，1-社医保，2-个税明细，3-国税账号，4-个税账号 */
    @NotNull(message = "业务类型不能为空")
    @Min(value = 1, message = "业务类型必须为1-4之间的数字")
    @Max(value = 4, message = "业务类型必须为1-4之间的数字")
    @ApiModelProperty(value = "业务类型：1-社医保，2-个税明细，3-国税账号，4-个税账号", required = true, allowableValues = "1,2,3,4")
    private Integer bizType;

    /** 业务类型名称（查询时返回） */
    @ApiModelProperty(value = "业务类型名称，查询时自动填充")
    private String bizTypeName;

    /** 录入方式，1-批量新增，2-单个新增 */
    @NotNull(message = "录入方式不能为空")
    @Min(value = 1, message = "录入方式必须为1或2")
    @Max(value = 2, message = "录入方式必须为1或2")
    @ApiModelProperty(value = "录入方式：1-批量新增，2-单个新增", required = true, allowableValues = "1,2")
    private Integer entryType;

    /** 录入方式名称（查询时返回） */
    @ApiModelProperty(value = "录入方式名称，查询时自动填充")
    private String entryTypeName;

    /** 操作方式，社医保/个税明细：1-提醒，2-更正，3-减员；国税账号：1-账号信息，2-会计实名；个税账号：1-个税账号添加 */
    @NotNull(message = "操作方式不能为空")
    @ApiModelProperty(value = "操作方式，根据业务类型不同含义不同", required = true)
    private Integer operationType;

    /** 操作方式名称（查询时返回） */
    @ApiModelProperty(value = "操作方式名称，查询时自动填充")
    private String operationTypeName;

    /** 员工姓名 */
    @NotBlank(message = "员工姓名不能为空")
    @Size(max = 100, message = "员工姓名长度不能超过100个字符")
    @ApiModelProperty(value = "员工姓名", required = true)
    private String employeeName;

    /** 身份证号 */
    @NotBlank(message = "身份证号不能为空")
    @IdNumber(message = "身份证号格式不正确")
    @ApiModelProperty(value = "身份证号", required = true)
    private String idNumber;

    /** 手机号 */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;

    /** 应发工资 */
    @DecimalMin(value = "0.00", message = "应发工资不能为负数")
    @Digits(integer = 8, fraction = 2, message = "应发工资格式不正确，最多8位整数2位小数")
    @ApiModelProperty(value = "应发工资")
    private BigDecimal grossSalary;

    /** 公积金（个人） */
    @DecimalMin(value = "0.00", message = "公积金不能为负数")
    @Digits(integer = 8, fraction = 2, message = "公积金格式不正确，最多8位整数2位小数")
    @ApiModelProperty(value = "公积金（个人）")
    private BigDecimal providentFundPersonal;

    /** 社保基数（仅社医保和个税明细业务使用） */
    @ApiModelProperty(value = "社保基数，仅社医保和个税明细业务使用")
    private BigDecimal socialInsuranceBase;

    /** 税号（国税账号/个税账号业务专用，可同信用代码） */
    @TaxNumber(allowEmpty = true, message = "税号格式不正确")
    @ApiModelProperty(value = "税号，国税账号/个税账号业务专用")
    private String taxNumber;

    /** 登录密码（国税账号/个税账号业务专用） */
    @Size(min = 6, max = 50, message = "登录密码长度必须为6-50位")
    @ApiModelProperty(value = "登录密码，国税账号/个税账号业务专用")
    private String queryPassword;

    /** 登录方式（个税账号业务专用） */
    @Size(max = 50, message = "登录方式长度不能超过50个字符")
    @ApiModelProperty(value = "登录方式，个税/国税账号业务专用")
    private String loginMethod;

    /** 实名经办人（个税账号业务专用） */
    @Size(max = 100, message = "实名经办人长度不能超过100个字符")
    @ApiModelProperty(value = "实名经办人，个税/国税账号业务专用")
    private String realNameAgent;

    /** 社保信息对象 */
    @Valid
    @SocialInsurancePackage(allowEmpty = true, message = "社保信息格式不正确")
    @ApiModelProperty(value = "社保信息对象，社医保业务专用")
    private SocialInsuranceVO socialInsurance;

    /** 扩展信息，JSON格式存储其他业务特定字段 */
    @ApiModelProperty(value = "扩展信息，JSON格式")
    private String extendInfo;

    /** 备注 */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @ApiModelProperty(value = "备注")
    private String remark;
}
