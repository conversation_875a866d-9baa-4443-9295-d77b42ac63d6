package com.bxm.customer.domain.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 增值员工操作类型枚举
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum ValueAddedOperationType {

    // 社医保/个税明细操作类型
    /**
     * 提醒
     */
    REMIND(1, "提醒"),

    /**
     * 更正
     */
    CORRECTION(2, "更正"),

    /**
     * 减员
     */
    REDUCTION(3, "减员"),

    // 国税账号操作类型
    /**
     * 账号信息
     */
    ACCOUNTING_REAL_NAME(1, "账号信息"),

    /**
     * 会计实名
     */
    REMOTE_REAL_NAME(2, "会计实名"),

    // 个税账号操作类型
    /**
     * 个税账号添加
     */
    PERSONAL_TAX_ACCOUNT_ADD(1, "个税账号添加");

    private final Integer code;
    private final String name;

    /**
     * 根据代码获取枚举
     *
     * @param code 操作类型代码
     * @return 对应的枚举值
     */
    public static ValueAddedOperationType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ValueAddedOperationType operationType : values()) {
            if (operationType.getCode().equals(code)) {
                return operationType;
            }
        }
        return null;
    }

    /**
     * 验证操作类型代码是否有效
     *
     * @param code 操作类型代码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 根据业务类型验证操作类型是否有效
     *
     * @param bizType 业务类型
     * @param operationType 操作类型代码
     * @return 是否有效
     */
    public static boolean isValidForBizType(ValueAddedBizType bizType, Integer operationType) {
        if (bizType == null || operationType == null) {
            return false;
        }

        switch (bizType) {
            case SOCIAL_INSURANCE:
            case PERSONAL_TAX:
                // 社医保和个税明细支持：1-提醒，2-更正，3-减员
                return operationType >= 1 && operationType <= 3;
            case NATIONAL_TAX_ACCOUNT:
                // 国税账号支持：1-账号信息，2-会计实名
                return operationType >= 1 && operationType <= 2;
            case PERSONAL_TAX_ACCOUNT:
                // 个税账号支持：1-个税账号添加
                return operationType == 1;
            default:
                return false;
        }
    }

    public static String getNameByForBizType(ValueAddedBizType bizType, Integer operationType) {
        if (bizType == null || operationType == null) {
            return "";
        }
        List<ValueAddedOperationType> operationTypesForBizType = getOperationTypesForBizType(bizType);
        for (ValueAddedOperationType valueAddedOperationType : operationTypesForBizType) {
            if (valueAddedOperationType.getCode().equals(operationType)) {
                return valueAddedOperationType.getName();
            }
        }
        return "";
    }

    private static List<ValueAddedOperationType> getOperationTypesForBizType(ValueAddedBizType bizType) {
        switch (bizType) {
            case SOCIAL_INSURANCE:
            case PERSONAL_TAX:
                return Lists.newArrayList(REMIND, CORRECTION, REDUCTION);
            case NATIONAL_TAX_ACCOUNT:
                return Lists.newArrayList(ACCOUNTING_REAL_NAME, REMOTE_REAL_NAME);
            case PERSONAL_TAX_ACCOUNT:
                return Lists.newArrayList(PERSONAL_TAX_ACCOUNT_ADD);
            default:
                return Lists.newArrayList();
        }
    }
}
